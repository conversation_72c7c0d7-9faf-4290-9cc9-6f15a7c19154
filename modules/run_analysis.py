#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片分析运行脚本
执行所有照片数据分析并生成报告
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.photo_analyzer import PhotoAnalyzer


def main():
    """主函数"""
    print("照片数据分析工具")
    print("=" * 50)
    
    # 创建分析器实例
    analyzer = PhotoAnalyzer()
    
    # 生成所有报告
    success = analyzer.generate_all_reports()
    
    if success:
        print("\n分析完成！请查看 data/reports/ 目录下的CSV报告文件。")
    else:
        print("\n分析失败，请检查数据文件是否存在。")


if __name__ == "__main__":
    main()
