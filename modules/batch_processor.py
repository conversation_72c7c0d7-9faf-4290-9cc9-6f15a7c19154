#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量照片处理器 - 目录批量处理模块
处理整个目录的照片，提取信息并保存到CSV文件

Author: ImageAPP
Version: 2.0.0
"""

import os
import csv
from .photo_reader import PhotoReader


class BatchProcessor:
    """批量照片处理类 - 处理目录中的所有照片"""

    def __init__(self):
        self.photo_reader = PhotoReader()
        self.supported_extensions = {'.jpg', '.jpeg', '.tiff', '.tif', '.png'}
        self.results = []

    def scan_directory(self, directory_path, recursive=True):
        """扫描目录中的所有图片文件"""
        image_files = []
        
        if recursive:
            # 递归扫描所有子目录
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    if os.path.splitext(file.lower())[1] in self.supported_extensions:
                        image_files.append(os.path.join(root, file))
        else:
            # 只扫描当前目录
            try:
                for file in os.listdir(directory_path):
                    file_path = os.path.join(directory_path, file)
                    if os.path.isfile(file_path) and os.path.splitext(file.lower())[1] in self.supported_extensions:
                        image_files.append(file_path)
            except OSError as e:
                print(f"❌ 无法访问目录 {directory_path}: {e}")
                return []
        
        return sorted(image_files)

    def process_directory(self, directory_path, output_csv=None, recursive=True, show_progress=True):
        """处理目录中的所有照片"""
        
        if show_progress:
            print(f"📁 扫描目录: {directory_path}")
            print("=" * 50)
        
        # 扫描图片文件
        image_files = self.scan_directory(directory_path, recursive)
        
        if not image_files:
            if show_progress:
                print("❌ 未找到任何图片文件")
            return []
        
        if show_progress:
            print(f"📸 找到 {len(image_files)} 个图片文件")
            print()
        
        # 处理照片
        self.results = []
        success_count = 0
        
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            
            if show_progress:
                print(f"[{i:3d}/{len(image_files)}] {filename}")
            
            if self.photo_reader.read_photo(image_path):
                photo_info = self.photo_reader.get_all_info()
                if photo_info:
                    photo_info['序号'] = i
                    self.results.append(photo_info)
                    success_count += 1
                    
                    if show_progress:
                        print(f"    ✅ 成功读取")
            else:
                if show_progress:
                    print(f"    ❌ 无法读取EXIF信息")
        
        if show_progress:
            print(f"\n📊 处理完成:")
            print(f"   总文件数: {len(image_files)}")
            print(f"   成功处理: {success_count}")
            print(f"   失败数量: {len(image_files) - success_count}")
        
        # 保存到CSV文件
        if output_csv and self.results:
            self.save_to_csv(output_csv, show_progress)
        
        return self.results

    def save_to_csv(self, output_csv, show_progress=True):
        """保存结果到CSV文件"""
        if not self.results:
            if show_progress:
                print("❌ 没有数据可保存")
            return False
        
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_csv)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 写入CSV文件
            with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = self.results[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.results)
            
            if show_progress:
                print(f"💾 结果已保存到: {output_csv}")
            
            return True
            
        except Exception as e:
            if show_progress:
                print(f"❌ 保存CSV文件失败: {e}")
            return False

    def get_statistics(self):
        """获取处理统计信息"""
        if not self.results:
            return {}
        
        stats = {
            'total_photos': len(self.results),
            'cameras': {},
            'lenses': {},
            'file_sizes': [],
            'dates': []
        }
        
        for photo in self.results:
            # 统计相机
            camera = photo.get('拍摄设备', '未知')
            stats['cameras'][camera] = stats['cameras'].get(camera, 0) + 1
            
            # 统计镜头
            lens = photo.get('镜头', '未知')
            stats['lenses'][lens] = stats['lenses'].get(lens, 0) + 1
            
            # 收集文件大小
            file_size = photo.get('文件大小(MB)', 0)
            try:
                file_size_float = float(file_size)
                if file_size_float > 0:
                    stats['file_sizes'].append(file_size_float)
            except (ValueError, TypeError):
                pass
            
            # 收集日期
            date = photo.get('拍摄日期', '')
            if date and date != '未知日期':
                stats['dates'].append(date)
        
        # 计算文件大小统计
        if stats['file_sizes']:
            stats['total_size_mb'] = sum(stats['file_sizes'])
            stats['avg_size_mb'] = round(stats['total_size_mb'] / len(stats['file_sizes']), 2)
            stats['min_size_mb'] = min(stats['file_sizes'])
            stats['max_size_mb'] = max(stats['file_sizes'])
        
        return stats

    def print_statistics(self):
        """打印处理统计信息"""
        stats = self.get_statistics()
        
        if not stats:
            print("❌ 没有统计数据")
            return
        
        print(f"\n📊 处理统计:")
        print("=" * 40)
        print(f"总照片数: {stats['total_photos']}")
        
        if 'total_size_mb' in stats:
            print(f"总文件大小: {stats['total_size_mb']:.2f} MB")
            print(f"平均文件大小: {stats['avg_size_mb']:.2f} MB")
            print(f"最小文件大小: {stats['min_size_mb']:.2f} MB")
            print(f"最大文件大小: {stats['max_size_mb']:.2f} MB")
        
        print(f"\n📷 相机统计:")
        for camera, count in sorted(stats['cameras'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {camera}: {count} 张")
        
        print(f"\n🔍 镜头统计:")
        for lens, count in sorted(stats['lenses'].items(), key=lambda x: x[1], reverse=True):
            if len(lens) > 50:  # 截断过长的镜头名称
                lens = lens[:47] + "..."
            print(f"  {lens}: {count} 张")


