#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成器
生成综合分析报告和汇总信息
"""

import pandas as pd
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.photo_analyzer import PhotoAnalyzer


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, report_dir="data/reports"):
        """
        初始化报告生成器
        
        Args:
            report_dir: 报告目录
        """
        self.report_dir = report_dir
        self.analyzer = PhotoAnalyzer()
    
    def generate_summary_report(self):
        """生成汇总报告"""
        if not self.analyzer.load_data():
            return False
        
        # 获取基本统计信息
        total_photos = len(self.analyzer.data)
        
        # 创建汇总报告内容
        summary_lines = [
            "照片数据分析汇总报告",
            "=" * 50,
            f"生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}",
            f"总照片数量: {total_photos}",
            "",
            "详细分析报告文件:",
            "- device_usage_report.csv (设备使用频率)",
            "- time_periods_report.csv (时间段分析)",
            "- seasonal_trends_report.csv (季节性趋势)",
            "- monthly_trends_report.csv (月度趋势)",
            "- lens_usage_report.csv (镜头使用频率)",
            "- focal_length_intervals_report.csv (焦距区间统计)",
            "- aperture_intervals_report.csv (光圈区间统计)",
            "- shutter_speed_intervals_report.csv (快门速度区间统计)",
            "- iso_intervals_report.csv (ISO区间统计)",
            "",
        ]
        
        # 添加快速统计信息
        if total_photos > 0:
            # 设备统计
            device_counts = self.analyzer.data['拍摄设备'].value_counts()
            summary_lines.extend([
                "快速统计信息:",
                "-" * 30,
                "主要拍摄设备:",
            ])
            for device, count in device_counts.head(3).items():
                percentage = (count / total_photos) * 100
                summary_lines.append(f"  {device}: {count}张 ({percentage:.1f}%)")
            
            # 镜头统计
            lens_counts = self.analyzer.data['镜头'].value_counts()
            summary_lines.extend([
                "",
                "主要使用镜头:",
            ])
            for lens, count in lens_counts.head(3).items():
                percentage = (count / total_photos) * 100
                clean_lens = lens.strip() if isinstance(lens, str) else str(lens)
                summary_lines.append(f"  {clean_lens}: {count}张 ({percentage:.1f}%)")
        
        # 保存汇总报告
        summary_path = os.path.join(self.report_dir, "summary_report.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary_lines))
        
        print(f"汇总报告已保存到: {summary_path}")
        return True
    
    def create_combined_technical_report(self):
        """创建技术参数综合报告"""
        if not self.analyzer.load_data():
            return False
        
        # 生成各个技术参数报告
        focal_df = self.analyzer.analyze_focal_length_intervals()
        aperture_df = self.analyzer.analyze_aperture_intervals()
        shutter_df = self.analyzer.analyze_shutter_speed_intervals()
        iso_df = self.analyzer.analyze_iso_intervals()
        
        # 创建综合技术报告
        combined_data = []
        
        # 添加焦距数据
        for _, row in focal_df.iterrows():
            combined_data.append({
                '参数类型': '焦距',
                '区间/类别': row['焦距区间'],
                '照片数量': row['照片数量'],
                '占比(%)': row['占比(%)']
            })
        
        # 添加光圈数据
        for _, row in aperture_df.iterrows():
            combined_data.append({
                '参数类型': '光圈',
                '区间/类别': row['光圈区间'],
                '照片数量': row['照片数量'],
                '占比(%)': row['占比(%)']
            })
        
        # 添加快门速度数据
        for _, row in shutter_df.iterrows():
            combined_data.append({
                '参数类型': '快门速度',
                '区间/类别': row['快门速度区间'],
                '照片数量': row['照片数量'],
                '占比(%)': row['占比(%)']
            })
        
        # 添加ISO数据
        for _, row in iso_df.iterrows():
            combined_data.append({
                '参数类型': 'ISO',
                '区间/类别': row['ISO区间'],
                '照片数量': row['照片数量'],
                '占比(%)': row['占比(%)']
            })
        
        # 保存综合技术报告
        combined_df = pd.DataFrame(combined_data)
        output_path = os.path.join(self.report_dir, "technical_parameters_combined_report.csv")
        combined_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"技术参数综合报告已保存到: {output_path}")
        
        return combined_df
    
    def generate_all_reports(self):
        """生成所有报告"""
        print("开始生成完整分析报告...")
        print("=" * 50)
        
        # 生成基础分析报告
        success = self.analyzer.generate_all_reports()
        if not success:
            return False
        
        # 生成汇总报告
        self.generate_summary_report()
        
        # 生成技术参数综合报告
        self.create_combined_technical_report()
        
        print("=" * 50)
        print("完整报告生成完成！")
        print(f"所有报告文件已保存到: {self.report_dir}")
        return True


def main():
    """主函数"""
    generator = ReportGenerator()
    generator.generate_all_reports()


if __name__ == "__main__":
    main()
