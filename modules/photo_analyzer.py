#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片数据分析模块
根据raw.csv文件生成各种统计报告
"""

import pandas as pd
import os
from datetime import datetime
import re
from collections import Counter


class PhotoAnalyzer:
    """照片数据分析器"""
    
    def __init__(self, csv_path="data/raw.csv", report_dir="data/reports"):
        """
        初始化分析器
        
        Args:
            csv_path: CSV数据文件路径
            report_dir: 报告输出目录
        """
        self.csv_path = csv_path
        self.report_dir = report_dir
        self.data = None
        
        # 确保报告目录存在
        os.makedirs(report_dir, exist_ok=True)
    
    def load_data(self):
        """加载CSV数据"""
        try:
            self.data = pd.read_csv(self.csv_path)
            print(f"成功加载 {len(self.data)} 条照片记录")
            return True
        except Exception as e:
            print(f"加载数据失败: {e}")
            return False
    
    def analyze_device_usage(self):
        """分析设备使用频率"""
        if self.data is None:
            return None
        
        device_counts = self.data['拍摄设备'].value_counts()
        total_photos = len(self.data)
        
        # 创建统计报告
        report = []
        for device, count in device_counts.items():
            percentage = (count / total_photos) * 100
            report.append({
                '拍摄设备': device,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })
        
        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "device_usage_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"设备使用频率报告已保存到: {output_path}")
        
        return report_df
    
    def analyze_time_periods(self):
        """分析一天中不同时间段的照片数量"""
        if self.data is None:
            return None
        
        # 定义时间段
        time_periods = {
            '凌晨(00:00-05:59)': (0, 6),
            '早晨(06:00-11:59)': (6, 12),
            '下午(12:00-17:59)': (12, 18),
            '晚上(18:00-23:59)': (18, 24)
        }
        
        # 解析时间并分类
        time_counts = {period: 0 for period in time_periods.keys()}
        
        for time_str in self.data['拍摄时间']:
            try:
                hour = int(time_str.split(':')[0])
                for period, (start, end) in time_periods.items():
                    if start <= hour < end:
                        time_counts[period] += 1
                        break
            except:
                continue
        
        # 创建报告
        total_photos = sum(time_counts.values())
        report = []
        for period, count in time_counts.items():
            percentage = (count / total_photos) * 100 if total_photos > 0 else 0
            report.append({
                '时间段': period,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })
        
        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "time_periods_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"时间段分析报告已保存到: {output_path}")
        
        return report_df
    
    def analyze_seasonal_trends(self):
        """分析季节性创作趋势"""
        if self.data is None:
            return None
        
        # 定义季节
        seasons = {
            '春季(3-5月)': [3, 4, 5],
            '夏季(6-8月)': [6, 7, 8],
            '秋季(9-11月)': [9, 10, 11],
            '冬季(12-2月)': [12, 1, 2]
        }
        
        # 解析日期并分类
        season_counts = {season: 0 for season in seasons.keys()}
        month_counts = {i: 0 for i in range(1, 13)}
        
        for date_str in self.data['拍摄日期']:
            try:
                # 解析日期格式 "2025年07月07日"
                year, month, day = re.findall(r'(\d{4})年(\d{2})月(\d{2})日', date_str)[0]
                month = int(month)
                month_counts[month] += 1
                
                # 分配到季节
                for season, months in seasons.items():
                    if month in months:
                        season_counts[season] += 1
                        break
            except:
                continue
        
        # 创建季节报告
        total_photos = sum(season_counts.values())
        season_report = []
        for season, count in season_counts.items():
            percentage = (count / total_photos) * 100 if total_photos > 0 else 0
            season_report.append({
                '季节': season,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })
        
        # 创建月度报告
        month_report = []
        for month, count in month_counts.items():
            percentage = (count / total_photos) * 100 if total_photos > 0 else 0
            month_report.append({
                '月份': f"{month}月",
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })
        
        # 保存报告
        season_df = pd.DataFrame(season_report)
        month_df = pd.DataFrame(month_report)
        
        season_path = os.path.join(self.report_dir, "seasonal_trends_report.csv")
        month_path = os.path.join(self.report_dir, "monthly_trends_report.csv")
        
        season_df.to_csv(season_path, index=False, encoding='utf-8-sig')
        month_df.to_csv(month_path, index=False, encoding='utf-8-sig')
        
        print(f"季节性趋势报告已保存到: {season_path}")
        print(f"月度趋势报告已保存到: {month_path}")
        
        return season_df, month_df
    
    def analyze_lens_usage(self):
        """分析镜头使用频率"""
        if self.data is None:
            return None
        
        lens_counts = self.data['镜头'].value_counts()
        total_photos = len(self.data)
        
        # 创建统计报告
        report = []
        for lens, count in lens_counts.items():
            percentage = (count / total_photos) * 100
            # 清理镜头名称（去除多余空格）
            clean_lens = lens.strip() if isinstance(lens, str) else str(lens)
            report.append({
                '镜头型号': clean_lens,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })
        
        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "lens_usage_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"镜头使用频率报告已保存到: {output_path}")
        
        return report_df

    def _extract_focal_length(self, focal_str):
        """从焦距字符串中提取等效全画幅焦距"""
        try:
            # 查找等效全画幅焦距
            match = re.search(r'等效全画幅焦距:\s*(\d+(?:\.\d+)?)mm', focal_str)
            if match:
                return float(match.group(1))
        except:
            pass
        return None

    def _extract_aperture(self, aperture_str):
        """从光圈字符串中提取f值"""
        try:
            # 提取f/数值
            match = re.search(r'f/(\d+(?:\.\d+)?)', aperture_str)
            if match:
                return float(match.group(1))
        except:
            pass
        return None

    def _extract_shutter_speed(self, shutter_str):
        """从快门速度字符串中提取数值（转换为秒）"""
        try:
            if '/' in shutter_str:
                # 处理分数形式，如 1/400s
                match = re.search(r'(\d+)/(\d+)s', shutter_str)
                if match:
                    numerator = float(match.group(1))
                    denominator = float(match.group(2))
                    return numerator / denominator
            else:
                # 处理整数形式，如 2s
                match = re.search(r'(\d+(?:\.\d+)?)s', shutter_str)
                if match:
                    return float(match.group(1))
        except:
            pass
        return None

    def _extract_iso(self, iso_str):
        """从ISO字符串中提取数值"""
        try:
            match = re.search(r'ISO\s*(\d+)', iso_str)
            if match:
                return int(match.group(1))
        except:
            pass
        return None

    def analyze_focal_length_intervals(self):
        """分析焦距区间统计"""
        if self.data is None:
            return None

        # 定义焦距区间
        intervals = [
            ('< 24mm (超广角)', lambda x: x < 24),
            ('24mm - 34mm (广角)', lambda x: 24 <= x <= 34),
            ('35mm - 59mm (标准/中等广角)', lambda x: 35 <= x <= 59),
            ('60mm - 104mm (中焦/中远摄)', lambda x: 60 <= x <= 104),
            ('105mm - 199mm (短长焦)', lambda x: 105 <= x <= 199),
            ('200mm - 299mm (中长焦)', lambda x: 200 <= x <= 299),
            ('300mm - 399mm (长长焦)', lambda x: 300 <= x <= 399),
            ('400mm - 599mm (超长焦)', lambda x: 400 <= x <= 599),
            ('>= 600mm (极致超长焦)', lambda x: x >= 600)
        ]

        # 统计各区间
        interval_counts = {name: 0 for name, _ in intervals}
        total_valid = 0

        for focal_str in self.data['焦距']:
            focal_length = self._extract_focal_length(str(focal_str))
            if focal_length is not None:
                total_valid += 1
                for name, condition in intervals:
                    if condition(focal_length):
                        interval_counts[name] += 1
                        break

        # 创建报告
        report = []
        for name, count in interval_counts.items():
            percentage = (count / total_valid) * 100 if total_valid > 0 else 0
            report.append({
                '焦距区间': name,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "focal_length_intervals_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"焦距区间统计报告已保存到: {output_path}")

        return report_df

    def analyze_aperture_intervals(self):
        """分析光圈区间统计"""
        if self.data is None:
            return None

        # 定义光圈区间
        intervals = [
            ('f/1.6 或更大', lambda x: x <= 1.6),
            ('f/1.8 - f/2.8', lambda x: 1.8 <= x <= 2.8),
            ('f/3.2 - f/4.0', lambda x: 3.2 <= x <= 4.0),
            ('f/4.5 - f/6.3', lambda x: 4.5 <= x <= 6.3),
            ('f/7.1 - f/9.0', lambda x: 7.1 <= x <= 9.0),
            ('f/10 - f/14', lambda x: 10 <= x <= 14),
            ('f/16 - f/22', lambda x: 16 <= x <= 22),
            ('> f/22', lambda x: x > 22)
        ]

        # 统计各区间
        interval_counts = {name: 0 for name, _ in intervals}
        total_valid = 0

        for aperture_str in self.data['光圈']:
            aperture = self._extract_aperture(str(aperture_str))
            if aperture is not None:
                total_valid += 1
                for name, condition in intervals:
                    if condition(aperture):
                        interval_counts[name] += 1
                        break

        # 创建报告
        report = []
        for name, count in interval_counts.items():
            percentage = (count / total_valid) * 100 if total_valid > 0 else 0
            report.append({
                '光圈区间': name,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "aperture_intervals_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"光圈区间统计报告已保存到: {output_path}")

        return report_df

    def analyze_shutter_speed_intervals(self):
        """分析快门速度区间统计"""
        if self.data is None:
            return None

        # 定义快门速度区间（以秒为单位）
        intervals = [
            ('1/2000s 及更快', lambda x: x <= 1/2000),
            ('1/500s - 1/1000s', lambda x: 1/1000 <= x <= 1/500),
            ('1/125s - 1/250s', lambda x: 1/250 <= x <= 1/125),
            ('1/15s - 1/60s', lambda x: 1/60 <= x <= 1/15),
            ('1s - 1/8s', lambda x: 1/8 <= x <= 1),
            ('> 1s (长时间曝光)', lambda x: x > 1)
        ]

        # 统计各区间
        interval_counts = {name: 0 for name, _ in intervals}
        total_valid = 0

        for shutter_str in self.data['快门']:
            shutter_speed = self._extract_shutter_speed(str(shutter_str))
            if shutter_speed is not None:
                total_valid += 1
                for name, condition in intervals:
                    if condition(shutter_speed):
                        interval_counts[name] += 1
                        break

        # 创建报告
        report = []
        for name, count in interval_counts.items():
            percentage = (count / total_valid) * 100 if total_valid > 0 else 0
            report.append({
                '快门速度区间': name,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "shutter_speed_intervals_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"快门速度区间统计报告已保存到: {output_path}")

        return report_df

    def analyze_iso_intervals(self):
        """分析ISO感光度区间统计"""
        if self.data is None:
            return None

        # 定义ISO区间
        intervals = [
            ('ISO 100 - ISO 400', lambda x: 100 <= x <= 400),
            ('ISO 800 - ISO 1600', lambda x: 800 <= x <= 1600),
            ('ISO 3200 - ISO 6400', lambda x: 3200 <= x <= 6400),
            ('> ISO 6400', lambda x: x > 6400)
        ]

        # 统计各区间
        interval_counts = {name: 0 for name, _ in intervals}
        total_valid = 0

        for iso_str in self.data['ISO']:
            iso_value = self._extract_iso(str(iso_str))
            if iso_value is not None:
                total_valid += 1
                for name, condition in intervals:
                    if condition(iso_value):
                        interval_counts[name] += 1
                        break

        # 创建报告
        report = []
        for name, count in interval_counts.items():
            percentage = (count / total_valid) * 100 if total_valid > 0 else 0
            report.append({
                'ISO区间': name,
                '照片数量': count,
                '占比(%)': round(percentage, 2)
            })

        # 保存到CSV
        report_df = pd.DataFrame(report)
        output_path = os.path.join(self.report_dir, "iso_intervals_report.csv")
        report_df.to_csv(output_path, index=False, encoding='utf-8-sig')
        print(f"ISO区间统计报告已保存到: {output_path}")

        return report_df

    def generate_all_reports(self):
        """生成所有分析报告"""
        if not self.load_data():
            return False

        print("开始生成照片分析报告...")
        print("=" * 50)

        # 生成各种报告
        self.analyze_device_usage()
        self.analyze_time_periods()
        self.analyze_seasonal_trends()
        self.analyze_lens_usage()
        self.analyze_focal_length_intervals()
        self.analyze_aperture_intervals()
        self.analyze_shutter_speed_intervals()
        self.analyze_iso_intervals()

        print("=" * 50)
        print("所有报告生成完成！")
        return True
