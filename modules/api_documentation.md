# ImageAPP 模块 API 文档

## 概述

ImageAPP 包含以下核心模块，提供照片信息读取、批量处理、重复检测和数据分析功能。

## 模块列表

### 1. PhotoReader - 照片信息读取器

**文件**: `photo_reader.py`

#### 类: PhotoReader

**初始化**
```python
PhotoReader()
```

**主要方法**

- `read_photo_info(image_path: str) -> dict`
  - 读取单张照片的元数据信息
  - 参数: `image_path` - 图片文件路径
  - 返回: 包含照片信息的字典

- `get_camera_info(exif_data) -> dict`
  - 提取相机设备信息
  - 参数: `exif_data` - EXIF数据
  - 返回: 相机信息字典

- `get_lens_info(exif_data) -> str`
  - 提取镜头信息
  - 参数: `exif_data` - EXIF数据
  - 返回: 镜头型号字符串

- `get_shooting_params(exif_data) -> dict`
  - 提取拍摄参数（光圈、快门、ISO、焦距）
  - 参数: `exif_data` - EXIF数据
  - 返回: 拍摄参数字典

- `get_location_info(exif_data) -> str`
  - 提取GPS位置信息
  - 参数: `exif_data` - EXIF数据
  - 返回: 位置信息字符串

- `get_datetime_info(exif_data) -> tuple`
  - 提取拍摄日期和时间
  - 参数: `exif_data` - EXIF数据
  - 返回: (日期字符串, 时间字符串) 元组

### 2. BatchProcessor - 批量处理器

**文件**: `batch_processor.py`

#### 类: BatchProcessor

**初始化**
```python
BatchProcessor(csv_path="data/raw.csv")
```
- 参数: `csv_path` - CSV输出文件路径

**主要方法**

- `process_directory(directory_path: str) -> bool`
  - 处理整个目录的照片
  - 参数: `directory_path` - 目录路径
  - 返回: 处理成功返回True

- `process_single_photo(image_path: str, sequence_number: int) -> bool`
  - 处理单张照片
  - 参数: `image_path` - 图片路径, `sequence_number` - 序号
  - 返回: 处理成功返回True

- `save_to_csv(photo_info: dict)`
  - 保存照片信息到CSV文件
  - 参数: `photo_info` - 照片信息字典

### 3. DuplicateDetector - 重复检测器

**文件**: `duplicate_detector.py`

#### 类: DuplicateDetector

**初始化**
```python
DuplicateDetector(csv_path="data/raw.csv")
```
- 参数: `csv_path` - CSV文件路径

**主要方法**

- `is_duplicate(photo_info: dict) -> bool`
  - 检测照片是否重复
  - 参数: `photo_info` - 照片信息字典
  - 返回: 重复返回True

- `load_existing_data()`
  - 加载已存在的照片数据

### 4. PhotoAnalyzer - 照片数据分析器

**文件**: `photo_analyzer.py`

#### 类: PhotoAnalyzer

**初始化**
```python
PhotoAnalyzer(csv_path="data/raw.csv", report_dir="data/reports")
```
- 参数: `csv_path` - 数据文件路径, `report_dir` - 报告输出目录

**主要方法**

**数据加载**
- `load_data() -> bool`
  - 加载CSV数据文件
  - 返回: 加载成功返回True

**基础分析方法**
- `analyze_device_usage() -> DataFrame`
  - 分析设备使用频率
  - 返回: 设备使用统计DataFrame

- `analyze_time_periods() -> DataFrame`
  - 分析时间段分布
  - 返回: 时间段统计DataFrame

- `analyze_seasonal_trends() -> tuple`
  - 分析季节性趋势
  - 返回: (季节统计DataFrame, 月度统计DataFrame)

- `analyze_lens_usage() -> DataFrame`
  - 分析镜头使用频率
  - 返回: 镜头使用统计DataFrame

**技术参数分析方法**
- `analyze_focal_length_intervals() -> DataFrame`
  - 分析焦距区间分布
  - 返回: 焦距区间统计DataFrame

- `analyze_aperture_intervals() -> DataFrame`
  - 分析光圈区间分布
  - 返回: 光圈区间统计DataFrame

- `analyze_shutter_speed_intervals() -> DataFrame`
  - 分析快门速度区间分布
  - 返回: 快门速度区间统计DataFrame

- `analyze_iso_intervals() -> DataFrame`
  - 分析ISO区间分布
  - 返回: ISO区间统计DataFrame

**综合方法**
- `generate_all_reports() -> bool`
  - 生成所有分析报告
  - 返回: 生成成功返回True

**内部辅助方法**
- `_extract_focal_length(focal_str: str) -> float`
  - 从焦距字符串提取数值

- `_extract_aperture(aperture_str: str) -> float`
  - 从光圈字符串提取数值

- `_extract_shutter_speed(shutter_str: str) -> float`
  - 从快门速度字符串提取数值

- `_extract_iso(iso_str: str) -> int`
  - 从ISO字符串提取数值

### 5. ReportGenerator - 报告生成器

**文件**: `report_generator.py`

#### 类: ReportGenerator

**初始化**
```python
ReportGenerator(report_dir="data/reports")
```
- 参数: `report_dir` - 报告输出目录

**主要方法**

- `generate_summary_report() -> bool`
  - 生成汇总报告
  - 返回: 生成成功返回True

- `create_combined_technical_report() -> DataFrame`
  - 创建技术参数综合报告
  - 返回: 综合技术报告DataFrame

- `generate_all_reports() -> bool`
  - 生成所有报告（包括基础分析、汇总和综合报告）
  - 返回: 生成成功返回True

## 使用示例

### 基础照片处理
```python
from modules import PhotoReader, BatchProcessor

# 处理单张照片
reader = PhotoReader()
photo_info = reader.read_photo_info("path/to/photo.jpg")

# 批量处理目录
processor = BatchProcessor()
processor.process_directory("path/to/photos")
```

### 数据分析
```python
from modules import PhotoAnalyzer, ReportGenerator

# 基础分析
analyzer = PhotoAnalyzer()
device_report = analyzer.analyze_device_usage()

# 完整报告生成
generator = ReportGenerator()
generator.generate_all_reports()
```

## 输出文件

### CSV数据文件
- `data/raw.csv` - 原始照片数据

### 分析报告文件（保存在 data/reports/）
- `device_usage_report.csv` - 设备使用频率
- `time_periods_report.csv` - 时间段分析
- `seasonal_trends_report.csv` - 季节性趋势
- `monthly_trends_report.csv` - 月度趋势
- `lens_usage_report.csv` - 镜头使用频率
- `focal_length_intervals_report.csv` - 焦距区间统计
- `aperture_intervals_report.csv` - 光圈区间统计
- `shutter_speed_intervals_report.csv` - 快门速度区间统计
- `iso_intervals_report.csv` - ISO区间统计
- `technical_parameters_combined_report.csv` - 技术参数综合报告
- `summary_report.txt` - 汇总报告

## 依赖要求

- `pandas` - 数据处理
- `Pillow (PIL)` - 图像处理
- `piexif` - EXIF数据读取

## 版本信息

当前版本: 2.1.0
