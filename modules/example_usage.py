#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片分析使用示例
演示如何使用PhotoAnalyzer和ReportGenerator
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.photo_analyzer import PhotoAnalyzer
from modules.report_generator import ReportGenerator


def example_individual_analysis():
    """示例：单独运行各种分析"""
    print("=== 单独分析示例 ===")
    
    analyzer = PhotoAnalyzer()
    
    if not analyzer.load_data():
        print("无法加载数据文件")
        return
    
    # 设备使用频率分析
    print("\n1. 设备使用频率分析:")
    device_report = analyzer.analyze_device_usage()
    if device_report is not None:
        print(device_report.to_string(index=False))
    
    # 时间段分析
    print("\n2. 时间段分析:")
    time_report = analyzer.analyze_time_periods()
    if time_report is not None:
        print(time_report.to_string(index=False))
    
    # 焦距区间分析
    print("\n3. 焦距区间分析:")
    focal_report = analyzer.analyze_focal_length_intervals()
    if focal_report is not None:
        print(focal_report.to_string(index=False))


def example_complete_analysis():
    """示例：完整分析报告生成"""
    print("\n=== 完整分析示例 ===")
    
    generator = ReportGenerator()
    success = generator.generate_all_reports()
    
    if success:
        print("完整分析报告生成成功！")
        print("请查看 data/reports/ 目录下的所有CSV文件")
    else:
        print("分析报告生成失败")


def main():
    """主函数"""
    print("照片分析使用示例")
    print("=" * 50)
    
    # 检查数据文件是否存在
    if not os.path.exists("data/raw.csv"):
        print("错误: 找不到数据文件 data/raw.csv")
        print("请先运行照片处理程序生成数据文件")
        return
    
    # 运行示例
    example_individual_analysis()
    example_complete_analysis()


if __name__ == "__main__":
    main()
