#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重复照片检测器 - 去除重复照片信息模块
检测和处理重复的照片信息，支持多种重复检测策略

Author: ImageAPP
Version: 2.0.0
"""

import os
import csv
import hashlib
from collections import defaultdict


class DuplicateDetector:
    """重复照片检测类 - 检测和处理重复照片"""

    def __init__(self):
        self.photos_data = []
        self.duplicates = []
        self.unique_photos = []

    def load_from_csv(self, csv_file):
        """从CSV文件加载照片数据"""
        self.photos_data = []
        
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    self.photos_data.append(row)
            
            print(f"✅ 从 {csv_file} 加载了 {len(self.photos_data)} 条照片记录")
            return True
            
        except Exception as e:
            print(f"❌ 加载CSV文件失败: {e}")
            return False

    def load_from_list(self, photos_list):
        """从照片列表加载数据"""
        self.photos_data = photos_list.copy()
        print(f"✅ 加载了 {len(self.photos_data)} 条照片记录")

    def detect_duplicates_by_filename(self):
        """根据文件名检测重复照片"""
        filename_groups = defaultdict(list)
        
        for i, photo in enumerate(self.photos_data):
            filename = photo.get('文件名', '')
            if filename:
                filename_groups[filename].append((i, photo))
        
        self.duplicates = []
        self.unique_photos = []
        processed_indices = set()
        
        for filename, photos in filename_groups.items():
            if len(photos) > 1:
                # 有重复文件名
                duplicate_group = {
                    'type': 'filename',
                    'key': filename,
                    'photos': photos
                }
                self.duplicates.append(duplicate_group)
                
                # 只保留第一个
                first_index, first_photo = photos[0]
                if first_index not in processed_indices:
                    self.unique_photos.append(first_photo)
                    processed_indices.add(first_index)
                
                # 标记其他为已处理
                for idx, _ in photos[1:]:
                    processed_indices.add(idx)
            else:
                # 唯一文件名
                index, photo = photos[0]
                if index not in processed_indices:
                    self.unique_photos.append(photo)
                    processed_indices.add(index)
        
        return len(self.duplicates)

    def detect_duplicates_by_content(self):
        """根据照片内容特征检测重复照片（基于EXIF信息）"""
        content_groups = defaultdict(list)
        
        for i, photo in enumerate(self.photos_data):
            # 创建内容特征键（基于拍摄时间、相机、镜头、拍摄参数）
            content_key = self._create_content_key(photo)
            if content_key:
                content_groups[content_key].append((i, photo))
        
        self.duplicates = []
        self.unique_photos = []
        processed_indices = set()
        
        for content_key, photos in content_groups.items():
            if len(photos) > 1:
                # 有重复内容
                duplicate_group = {
                    'type': 'content',
                    'key': content_key,
                    'photos': photos
                }
                self.duplicates.append(duplicate_group)
                
                # 保留文件大小最大的（通常质量更好）
                best_photo = max(photos, key=lambda x: float(x[1].get('文件大小(MB)', 0)))
                best_index, best_photo_data = best_photo
                
                if best_index not in processed_indices:
                    self.unique_photos.append(best_photo_data)
                    processed_indices.add(best_index)
                
                # 标记其他为已处理
                for idx, _ in photos:
                    if idx != best_index:
                        processed_indices.add(idx)
            else:
                # 唯一内容
                index, photo = photos[0]
                if index not in processed_indices:
                    self.unique_photos.append(photo)
                    processed_indices.add(index)
        
        return len(self.duplicates)

    def detect_duplicates_by_file_hash(self):
        """根据文件哈希检测重复照片（需要文件路径有效）"""
        hash_groups = defaultdict(list)
        
        for i, photo in enumerate(self.photos_data):
            file_path = photo.get('文件路径', '')
            if file_path and os.path.exists(file_path):
                file_hash = self._calculate_file_hash(file_path)
                if file_hash:
                    hash_groups[file_hash].append((i, photo))
        
        self.duplicates = []
        self.unique_photos = []
        processed_indices = set()
        
        for file_hash, photos in hash_groups.items():
            if len(photos) > 1:
                # 有重复文件
                duplicate_group = {
                    'type': 'file_hash',
                    'key': file_hash,
                    'photos': photos
                }
                self.duplicates.append(duplicate_group)
                
                # 只保留第一个
                first_index, first_photo = photos[0]
                if first_index not in processed_indices:
                    self.unique_photos.append(first_photo)
                    processed_indices.add(first_index)
                
                # 标记其他为已处理
                for idx, _ in photos[1:]:
                    processed_indices.add(idx)
            else:
                # 唯一文件
                index, photo = photos[0]
                if index not in processed_indices:
                    self.unique_photos.append(photo)
                    processed_indices.add(index)
        
        return len(self.duplicates)

    def _create_content_key(self, photo):
        """创建照片内容特征键"""
        key_parts = []
        
        # 关键字段用于判断是否为同一张照片
        key_fields = ['拍摄日期', '拍摄时间', '拍摄设备', '镜头', '光圈', '快门', 'ISO', '焦距']
        
        for field in key_fields:
            value = photo.get(field, '')
            if value and value != '未知':
                key_parts.append(f"{field}:{value}")
        
        if len(key_parts) >= 3:  # 至少要有3个有效字段才能作为特征
            return '|'.join(key_parts)
        
        return None

    def _calculate_file_hash(self, file_path, chunk_size=8192):
        """计算文件的MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(chunk_size), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"❌ 计算文件哈希失败 {file_path}: {e}")
            return None

    def remove_duplicates(self, method='content', keep_best=True):
        """移除重复照片"""
        if method == 'filename':
            duplicate_count = self.detect_duplicates_by_filename()
        elif method == 'content':
            duplicate_count = self.detect_duplicates_by_content()
        elif method == 'file_hash':
            duplicate_count = self.detect_duplicates_by_file_hash()
        else:
            print(f"❌ 不支持的检测方法: {method}")
            return False
        
        print(f"🔍 检测方法: {method}")
        print(f"📊 原始照片数: {len(self.photos_data)}")
        print(f"🔄 发现重复组: {duplicate_count}")
        print(f"✅ 去重后照片数: {len(self.unique_photos)}")
        print(f"🗑️  移除重复照片: {len(self.photos_data) - len(self.unique_photos)}")
        
        return True

    def save_unique_photos(self, output_csv):
        """保存去重后的照片信息到CSV"""
        if not self.unique_photos:
            print("❌ 没有去重后的照片数据")
            return False
        
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_csv)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 重新编号
            for i, photo in enumerate(self.unique_photos, 1):
                photo['序号'] = i
            
            # 写入CSV文件
            with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
                if self.unique_photos:
                    fieldnames = self.unique_photos[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(self.unique_photos)
            
            print(f"💾 去重后的照片信息已保存到: {output_csv}")
            return True
            
        except Exception as e:
            print(f"❌ 保存CSV文件失败: {e}")
            return False

    def save_duplicate_report(self, output_csv):
        """保存重复照片报告到CSV"""
        if not self.duplicates:
            print("❌ 没有重复照片数据")
            return False
        
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_csv)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 准备重复照片报告数据
            report_data = []
            for i, duplicate_group in enumerate(self.duplicates, 1):
                group_type = duplicate_group['type']
                group_key = duplicate_group['key']
                photos = duplicate_group['photos']
                
                for j, (original_index, photo) in enumerate(photos):
                    report_row = photo.copy()
                    report_row['重复组号'] = i
                    report_row['重复类型'] = group_type
                    report_row['重复特征'] = group_key
                    report_row['组内序号'] = j + 1
                    report_row['是否保留'] = '是' if j == 0 else '否'  # 默认保留第一个
                    report_data.append(report_row)
            
            # 写入CSV文件
            with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
                if report_data:
                    fieldnames = report_data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(report_data)
            
            print(f"📋 重复照片报告已保存到: {output_csv}")
            return True
            
        except Exception as e:
            print(f"❌ 保存重复照片报告失败: {e}")
            return False

    def print_duplicate_summary(self):
        """打印重复照片摘要"""
        if not self.duplicates:
            print("✅ 没有发现重复照片")
            return
        
        print(f"\n🔄 重复照片摘要:")
        print("=" * 50)
        
        for i, duplicate_group in enumerate(self.duplicates, 1):
            group_type = duplicate_group['type']
            group_key = duplicate_group['key']
            photos = duplicate_group['photos']
            
            print(f"\n重复组 {i} ({group_type}):")
            if len(group_key) > 80:
                print(f"  特征: {group_key[:77]}...")
            else:
                print(f"  特征: {group_key}")
            print(f"  重复数量: {len(photos)}")
            
            for j, (_, photo) in enumerate(photos):
                filename = photo.get('文件名', '未知')
                file_size = photo.get('文件大小(MB)', '0')
                status = "✅ 保留" if j == 0 else "🗑️  删除"
                print(f"    {j+1}. {filename} ({file_size}MB) {status}")

    def get_duplicate_statistics(self):
        """获取重复检测统计信息"""
        if not hasattr(self, 'photos_data') or not self.photos_data:
            return {}
        
        stats = {
            'original_count': len(self.photos_data),
            'unique_count': len(self.unique_photos),
            'duplicate_groups': len(self.duplicates),
            'removed_count': len(self.photos_data) - len(self.unique_photos),
            'duplicate_rate': 0
        }
        
        if stats['original_count'] > 0:
            stats['duplicate_rate'] = round((stats['removed_count'] / stats['original_count']) * 100, 2)
        
        return stats
