#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
照片信息读取器 - 单张照片处理模块
读取照片的EXIF信息，包括拍摄设备、镜头、光圈、快门速度、ISO、等效全画幅焦距、位置、日期时间等

Author: ImageAPP
Version: 2.0.0
"""

import os
from PIL import Image
from PIL.ExifTags import TAGS, GPSTAGS
from datetime import datetime


class PhotoReader:
    """照片信息读取类 - 处理单张照片"""

    def __init__(self):
        self.exif_data = {}
        self.gps_data = {}
        self.image_path = None

    def read_photo(self, image_path):
        """读取照片的EXIF信息"""
        self.image_path = image_path
        self.exif_data = {}
        self.gps_data = {}
        
        try:
            # 打开图片
            image = Image.open(image_path)

            # 获取EXIF数据
            exifdata = image.getexif()

            if not exifdata:
                return False

            # 解析主EXIF数据
            for tag_id in exifdata:
                tag = TAGS.get(tag_id, tag_id)
                data = exifdata.get(tag_id)
                self.exif_data[tag] = data

            # 读取EXIF IFD子数据（包含拍摄参数）
            if 34665 in exifdata:  # ExifOffset
                try:
                    exif_ifd = exifdata.get_ifd(34665)
                    for tag_id in exif_ifd:
                        tag = TAGS.get(tag_id, tag_id)
                        data = exif_ifd.get(tag_id)
                        self.exif_data[tag] = data
                except Exception as e:
                    print(f"读取EXIF IFD失败: {e}")

            # 读取GPS IFD子数据
            if 34853 in exifdata:  # GPSInfo
                try:
                    gps_ifd = exifdata.get_ifd(34853)
                    for tag_id in gps_ifd:
                        tag = GPSTAGS.get(tag_id, tag_id)
                        data = gps_ifd.get(tag_id)
                        self.gps_data[tag] = data
                except Exception as e:
                    print(f"读取GPS IFD失败: {e}")

            return True

        except Exception as e:
            print(f"读取图片失败: {e}")
            return False

    def _convert_to_degrees(self, value):
        """将GPS坐标转换为度数"""
        d, m, s = value
        return d + (m / 60.0) + (s / 3600.0)

    def get_camera_info(self):
        """获取相机信息"""
        make = self.exif_data.get('Make', '未知')
        model = self.exif_data.get('Model', '未知')
        return f"{make} {model}".strip()

    def get_lens_info(self):
        """获取镜头信息"""
        lens_make = self.exif_data.get('LensMake', '')
        lens_model = self.exif_data.get('LensModel', '')
        lens_spec = self.exif_data.get('LensSpecification', '')

        if lens_model:
            return f"{lens_make} {lens_model}".strip() if lens_make else lens_model
        elif lens_spec:
            return f"镜头规格: {lens_spec}"
        else:
            return "未知镜头"

    def get_aperture(self):
        """获取光圈值"""
        # 尝试多个可能的光圈字段
        aperture_fields = ['FNumber', 'ApertureValue', 'MaxApertureValue']

        for field in aperture_fields:
            aperture = self.exif_data.get(field, None)
            if aperture:
                if field == 'FNumber':
                    return f"f/{aperture}"
                elif field in ['ApertureValue', 'MaxApertureValue']:
                    try:
                        f_number = round(2 ** (aperture / 2), 1)
                        return f"f/{f_number}"
                    except:
                        continue

        return "未知"

    def get_shutter_speed(self):
        """获取快门速度"""
        exposure_time = self.exif_data.get('ExposureTime', None)
        if exposure_time:
            if exposure_time < 1:
                return f"1/{int(1/exposure_time)}s"
            else:
                return f"{exposure_time}s"

        # 尝试从ShutterSpeedValue获取
        shutter_speed_value = self.exif_data.get('ShutterSpeedValue', None)
        if shutter_speed_value:
            exposure_time = 1 / (2 ** shutter_speed_value)
            if exposure_time < 1:
                return f"1/{int(1/exposure_time)}s"
            else:
                return f"{exposure_time}s"

        return "未知"

    def get_iso(self):
        """获取ISO值"""
        # 尝试多个可能的ISO字段
        iso_fields = ['ISOSpeedRatings', 'ISO', 'PhotographicSensitivity', 'RecommendedExposureIndex']

        for field in iso_fields:
            iso = self.exif_data.get(field, None)
            if iso is not None:
                return f"ISO {iso}"

        return "未知"

    def get_focal_length(self):
        """获取焦距信息"""
        focal_length = self.exif_data.get('FocalLength', None)
        focal_length_35mm = self.exif_data.get('FocalLengthIn35mmFilm', None)

        result = []
        if focal_length:
            result.append(f"实际焦距: {focal_length}mm")

        if focal_length_35mm:
            result.append(f"等效全画幅焦距: {focal_length_35mm}mm")
        elif focal_length:
            # 如果没有35mm等效焦距，尝试计算
            crop_factor = self._get_crop_factor()
            if crop_factor:
                equiv_focal_length = round(focal_length * crop_factor)
                result.append(f"等效全画幅焦距: {equiv_focal_length}mm (估算)")

        return " | ".join(result) if result else "未知"

    def _get_crop_factor(self):
        """根据相机型号估算裁切系数"""
        camera_model = self.exif_data.get('Model', '').upper()

        # 一些常见相机的裁切系数
        crop_factors = {
            'MICRO FOUR THIRDS': 2.0,
            'M4/3': 2.0,
            'APS-C': 1.5,
            'DX': 1.5,  # Nikon DX
        }

        for key, factor in crop_factors.items():
            if key in camera_model:
                return factor

        return None

    def get_location(self):
        """获取拍摄位置"""
        if not self.gps_data:
            return "未知位置"

        try:
            # 获取纬度
            lat_ref = self.gps_data.get('GPSLatitudeRef', '')
            lat = self.gps_data.get('GPSLatitude', None)

            # 获取经度
            lon_ref = self.gps_data.get('GPSLongitudeRef', '')
            lon = self.gps_data.get('GPSLongitude', None)

            if lat and lon:
                lat_deg = self._convert_to_degrees(lat)
                lon_deg = self._convert_to_degrees(lon)

                # 根据参考方向调整符号
                if lat_ref == 'S':
                    lat_deg = -lat_deg
                if lon_ref == 'W':
                    lon_deg = -lon_deg

                # 获取GPS时间戳（如果有的话）
                gps_time = self.gps_data.get('GPSTimeStamp', None)
                gps_date = self.gps_data.get('GPSDateStamp', None)

                location_str = f"纬度: {lat_deg:.6f}°, 经度: {lon_deg:.6f}°"

                if gps_date and gps_time:
                    try:
                        gps_datetime = f"{gps_date} {int(gps_time[0]):02d}:{int(gps_time[1]):02d}:{int(gps_time[2]):02d}"
                        location_str += f" (GPS时间: {gps_datetime})"
                    except:
                        pass

                return location_str

        except Exception as e:
            print(f"解析GPS信息时出错: {e}")

        return "未知位置"

    def get_datetime(self):
        """获取拍摄日期和时间"""
        # 尝试多个可能的日期时间字段
        datetime_fields = ['DateTime', 'DateTimeOriginal', 'DateTimeDigitized']

        for field in datetime_fields:
            dt_str = self.exif_data.get(field, None)
            if dt_str:
                try:
                    # 解析日期时间字符串
                    dt = datetime.strptime(dt_str, '%Y:%m:%d %H:%M:%S')
                    return {
                        'date': dt.strftime('%Y年%m月%d日'),
                        'time': dt.strftime('%H:%M:%S'),
                        'datetime': dt.strftime('%Y年%m月%d日 %H:%M:%S')
                    }
                except ValueError:
                    continue

        return {
            'date': '未知日期',
            'time': '未知时间',
            'datetime': '未知日期时间'
        }

    def get_file_info(self):
        """获取文件基本信息"""
        if not self.image_path:
            return {}
        
        try:
            file_size_mb = round(os.path.getsize(self.image_path) / (1024 * 1024), 2)
            filename = os.path.basename(self.image_path)
            return {
                'filename': filename,
                'filepath': self.image_path,
                'file_size_mb': file_size_mb
            }
        except:
            return {
                'filename': os.path.basename(self.image_path) if self.image_path else '未知',
                'filepath': self.image_path or '未知',
                'file_size_mb': 0
            }

    def get_all_info(self):
        """获取照片的所有信息"""
        if not self.image_path:
            return None
        
        file_info = self.get_file_info()
        datetime_info = self.get_datetime()
        
        return {
            '文件名': file_info['filename'],
            '文件路径': file_info['filepath'],
            '文件大小(MB)': file_info['file_size_mb'],
            '拍摄设备': self.get_camera_info(),
            '镜头': self.get_lens_info(),
            '光圈': self.get_aperture(),
            '快门': self.get_shutter_speed(),
            'ISO': self.get_iso(),
            '焦距': self.get_focal_length(),
            '位置': self.get_location(),
            '拍摄日期': datetime_info['date'],
            '拍摄时间': datetime_info['time']
        }

    def print_photo_info(self):
        """打印照片的详细信息"""
        if not self.image_path:
            print("❌ 未加载照片")
            return
        
        filename = os.path.basename(self.image_path)
        print(f"\n{'='*60}")
        print(f"照片信息: {filename}")
        print(f"{'='*60}")

        # 基本信息
        print(f"📷 拍摄设备: {self.get_camera_info()}")
        print(f"🔍 镜头信息: {self.get_lens_info()}")

        # 拍摄参数
        print(f"🔧 光圈: {self.get_aperture()}")
        print(f"⚡ 快门速度: {self.get_shutter_speed()}")
        print(f"📊 ISO: {self.get_iso()}")
        print(f"📏 焦距: {self.get_focal_length()}")

        # 位置和时间
        print(f"📍 拍摄位置: {self.get_location()}")

        datetime_info = self.get_datetime()
        print(f"📅 拍摄日期: {datetime_info['date']}")
        print(f"🕐 拍摄时间: {datetime_info['time']}")

        print(f"{'='*60}\n")
