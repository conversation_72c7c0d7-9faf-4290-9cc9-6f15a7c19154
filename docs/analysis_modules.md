# 照片分析模块文档

## 概述

照片分析模块提供了对照片元数据的统计分析功能，可以生成各种详细的分析报告。

## 模块说明

### 1. PhotoAnalyzer (photo_analyzer.py)

主要的照片数据分析器，提供以下分析功能：

#### 基础分析
- **设备使用频率分析** (`analyze_device_usage()`)
  - 统计不同拍摄设备的使用次数和占比
  - 输出文件：`device_usage_report.csv`

- **时间段分析** (`analyze_time_periods()`)
  - 统计一天中不同时间段的照片数量
  - 时间段划分：凌晨、早晨、下午、晚上
  - 输出文件：`time_periods_report.csv`

- **季节性趋势分析** (`analyze_seasonal_trends()`)
  - 统计不同季节和月份的照片数量
  - 输出文件：`seasonal_trends_report.csv`, `monthly_trends_report.csv`

- **镜头使用频率分析** (`analyze_lens_usage()`)
  - 统计各镜头型号的使用次数和占比
  - 输出文件：`lens_usage_report.csv`

#### 技术参数区间分析

- **焦距区间统计** (`analyze_focal_length_intervals()`)
  - 按照预定义区间统计焦距分布
  - 区间包括：超广角、广角、标准、中焦、长焦等
  - 输出文件：`focal_length_intervals_report.csv`

- **光圈区间统计** (`analyze_aperture_intervals()`)
  - 按照光圈值区间统计分布
  - 区间从f/1.6到f/22+
  - 输出文件：`aperture_intervals_report.csv`

- **快门速度区间统计** (`analyze_shutter_speed_intervals()`)
  - 按照快门速度区间统计分布
  - 从高速快门到长时间曝光
  - 输出文件：`shutter_speed_intervals_report.csv`

- **ISO区间统计** (`analyze_iso_intervals()`)
  - 按照ISO感光度区间统计分布
  - 从低ISO到高ISO区间
  - 输出文件：`iso_intervals_report.csv`

### 2. ReportGenerator (report_generator.py)

综合报告生成器，提供以下功能：

- **汇总报告生成** (`generate_summary_report()`)
  - 生成包含基本统计信息的文本报告
  - 输出文件：`summary_report.txt`

- **技术参数综合报告** (`create_combined_technical_report()`)
  - 将所有技术参数分析合并到一个CSV文件
  - 输出文件：`technical_parameters_combined_report.csv`

- **完整报告生成** (`generate_all_reports()`)
  - 一键生成所有分析报告

## 使用方法

### 方法1：使用运行脚本

```bash
# 生成基础分析报告
python modules/run_analysis.py

# 生成完整分析报告（包括汇总）
python modules/report_generator.py
```

### 方法2：在代码中使用

```python
from modules.photo_analyzer import PhotoAnalyzer
from modules.report_generator import ReportGenerator

# 基础分析
analyzer = PhotoAnalyzer()
analyzer.generate_all_reports()

# 完整报告生成
generator = ReportGenerator()
generator.generate_all_reports()
```

### 方法3：单独分析

```python
from modules.photo_analyzer import PhotoAnalyzer

analyzer = PhotoAnalyzer()
analyzer.load_data()

# 单独运行某个分析
device_report = analyzer.analyze_device_usage()
focal_report = analyzer.analyze_focal_length_intervals()
```

## 输出文件说明

所有报告文件保存在 `data/reports/` 目录下：

### CSV报告文件
- `device_usage_report.csv` - 设备使用频率
- `time_periods_report.csv` - 时间段分析
- `seasonal_trends_report.csv` - 季节性趋势
- `monthly_trends_report.csv` - 月度趋势
- `lens_usage_report.csv` - 镜头使用频率
- `focal_length_intervals_report.csv` - 焦距区间统计
- `aperture_intervals_report.csv` - 光圈区间统计
- `shutter_speed_intervals_report.csv` - 快门速度区间统计
- `iso_intervals_report.csv` - ISO区间统计
- `technical_parameters_combined_report.csv` - 技术参数综合报告

### 文本报告文件
- `summary_report.txt` - 汇总报告

## 技术参数区间定义

### 焦距区间 (35mm等效)
- < 24mm (超广角)
- 24mm - 34mm (广角)
- 35mm - 59mm (标准/中等广角)
- 60mm - 104mm (中焦/中远摄)
- 105mm - 199mm (短长焦)
- 200mm - 299mm (中长焦)
- 300mm - 399mm (长长焦)
- 400mm - 599mm (超长焦)
- >= 600mm (极致超长焦)

### 光圈区间
- f/1.6 或更大
- f/1.8 - f/2.8
- f/3.2 - f/4.0
- f/4.5 - f/6.3
- f/7.1 - f/9.0
- f/10 - f/14
- f/16 - f/22
- > f/22

### 快门速度区间
- 1/2000s 及更快
- 1/500s - 1/1000s
- 1/125s - 1/250s
- 1/15s - 1/60s
- 1s - 1/8s
- > 1s (长时间曝光)

### ISO区间
- ISO 100 - ISO 400
- ISO 800 - ISO 1600
- ISO 3200 - ISO 6400
- > ISO 6400

## 依赖要求

- pandas
- 现有的 `data/raw.csv` 文件（由照片处理模块生成）

## 注意事项

1. 确保 `data/raw.csv` 文件存在且格式正确
2. 报告文件会自动创建 `data/reports/` 目录
3. 重复运行会覆盖之前的报告文件
4. 所有CSV文件使用UTF-8编码，支持中文显示
