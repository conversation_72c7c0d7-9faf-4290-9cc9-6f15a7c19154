# Modules API 文档

ImageAPP 核心模块API说明文档。

## 核心模块

### PhotoReader - 单张照片读取器

```python
from modules import PhotoReader

reader = PhotoReader()
if reader.read_photo('photo.jpg'):
    reader.print_photo_info()
    info = reader.get_all_info()
```

**主要方法:**
- `read_photo(image_path)`: 读取照片EXIF信息
- `get_camera_info()`: 获取相机信息
- `get_lens_info()`: 获取镜头信息
- `get_aperture()`: 获取光圈值
- `get_shutter_speed()`: 获取快门速度
- `get_iso()`: 获取ISO值
- `get_focal_length()`: 获取焦距信息
- `get_location()`: 获取GPS位置
- `get_datetime()`: 获取拍摄时间
- `get_all_info()`: 获取所有信息的字典

### BatchProcessor - 批量处理器

```python
from modules import BatchProcessor

processor = BatchProcessor()
results = processor.process_directory('Images/', 'data/raw.csv')
processor.print_statistics()
```

**主要方法:**
- `process_directory()`: 批量处理目录
- `save_to_csv()`: 保存结果到CSV
- `get_statistics()`: 获取统计信息
- `print_statistics()`: 打印统计信息

### DuplicateDetector - 重复检测器

```python
from modules import DuplicateDetector

detector = DuplicateDetector()
detector.load_from_csv('data/raw.csv')
detector.remove_duplicates(method='content')
detector.save_unique_photos('data/raw.csv')
```

**主要方法:**
- `load_from_csv()`: 从CSV加载数据
- `remove_duplicates()`: 移除重复照片
- `save_unique_photos()`: 保存去重结果
- `print_duplicate_summary()`: 打印重复摘要

## 使用示例

### 完整工作流程

```python
from modules import BatchProcessor, DuplicateDetector

# 1. 批量处理照片
processor = BatchProcessor()
results = processor.process_directory('Images/', 'data/raw.csv')

# 2. 重复检测
detector = DuplicateDetector()
detector.load_from_csv('data/raw.csv')
detector.remove_duplicates(method='content')
detector.save_unique_photos('data/raw.csv')

# 3. 显示统计
processor.print_statistics()
```

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- TIFF (.tiff, .tif)
- PNG (.png)

## 依赖要求

- Python 3.6+
- Pillow (PIL)
