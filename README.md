# ImageAPP - 照片信息提取与分析工具

一个功能强大的照片EXIF信息提取和数据分析工具，支持批量处理、重复检测和统计分析。

## 🌟 主要功能

- 📸 **单张照片处理**: 读取和显示单张照片的详细EXIF信息
- 📁 **批量目录处理**: 递归扫描目录，批量处理所有照片
- 🔍 **重复照片检测**: 自动检测和去除重复照片
- 💾 **统一数据管理**: 所有信息保存到 `data/raw.csv` 文件
- 📊 **数据分析**: 生成详细的照片统计分析报告
- 📈 **多维度统计**: 设备使用、时间分布、技术参数等多角度分析

## 🚀 快速开始

### 安装依赖

```bash
pip install Pillow
```

### 基本使用

#### 1. 照片数据分析（推荐）

```bash
# 快速生成基础分析报告
python run_analysis.py

# 生成完整分析报告（包括汇总）
python generate_reports.py

# 查看使用示例
python example_usage.py
```

#### 2. 批量处理照片

```bash
# 处理Images目录下的所有照片，自动去重并保存到data/raw.csv
python usage/test_batch_processor.py Images/
```

#### 3. 完整工作流程

```bash
# 执行完整的处理流程
python usage/complete_workflow_example.py Images/
```

## 📁 项目结构

```text
ImageAPP/
├── modules/                        # 核心功能模块
│   ├── photo_reader.py            # 单张照片读取器
│   ├── batch_processor.py         # 批量处理器
│   ├── duplicate_detector.py      # 重复检测器
│   ├── photo_analyzer.py          # 照片数据分析器
│   ├── report_generator.py        # 报告生成器
│   └── api_documentation.md       # API文档
├── data/                          # 数据文件
│   ├── raw.csv                   # 主要数据文件（去重后的照片信息）
│   └── reports/                  # 分析报告文件夹
├── docs/                          # 项目文档
│   ├── modules_api.md            # 原有API文档
│   └── analysis_modules.md       # 分析模块文档
├── usage/                         # 测试和示例（原有）
├── run_analysis.py               # 快速分析脚本
├── generate_reports.py           # 完整报告生成脚本
└── example_usage.py              # 使用示例脚本
```

## 📊 数据分析功能

### 分析报告类型

- **设备使用频率**: 统计不同拍摄设备的使用次数和占比
- **时间段分析**: 统计一天中不同时间段的照片数量分布
- **季节性趋势**: 统计不同季节和月份的照片数量变化
- **镜头使用频率**: 统计各镜头型号的使用情况
- **技术参数区间分析**:
  - 焦距区间统计（超广角到极致超长焦）
  - 光圈区间统计（f/1.6到f/22+）
  - 快门速度区间统计（高速到长曝光）
  - ISO区间统计（低ISO到高ISO）

### 报告文件

所有分析报告保存在 `data/reports/` 目录：

- **CSV格式报告**: 详细的统计数据表格
- **TXT格式汇总**: 包含快速统计信息的文本报告
- **综合技术报告**: 所有技术参数的合并分析

## 📁 数据文件说明

### 主要数据文件

- **data/raw.csv**: 包含所有不重复的照片信息
  - 批量处理后自动去重
  - 包含完整的EXIF信息
  - 作为所有后续处理的数据源

### CSV文件字段

- **文件信息**: 文件名、文件路径、文件大小(MB)
- **设备信息**: 拍摄设备、镜头
- **拍摄参数**: 光圈、快门、ISO、焦距
- **位置时间**: 位置、拍摄日期、拍摄时间

## 🔧 核心模块

### PhotoReader - 单张照片读取器
```python
from modules import PhotoReader

reader = PhotoReader()
if reader.read_photo('photo.jpg'):
    reader.print_photo_info()
    info = reader.get_all_info()
```

### BatchProcessor - 批量处理器
```python
from modules import BatchProcessor

processor = BatchProcessor()
results = processor.process_directory('Images/', 'data/raw.csv')
processor.print_statistics()
```

### DuplicateDetector - 重复检测器

```python
from modules import DuplicateDetector

detector = DuplicateDetector()
detector.load_from_csv('data/raw.csv')
detector.remove_duplicates(method='content')
detector.save_unique_photos('data/raw.csv')
```

### PhotoAnalyzer - 照片数据分析器

```python
from modules import PhotoAnalyzer

analyzer = PhotoAnalyzer()
analyzer.generate_all_reports()  # 生成所有分析报告

# 或单独运行某个分析
device_report = analyzer.analyze_device_usage()
focal_report = analyzer.analyze_focal_length_intervals()
```

### ReportGenerator - 报告生成器

```python
from modules import ReportGenerator

generator = ReportGenerator()
generator.generate_all_reports()  # 生成完整报告（包括汇总）
```

## 📚 文档

- [模块API文档](modules/api_documentation.md) - 完整的API说明
- [分析模块文档](docs/analysis_modules.md) - 数据分析功能详解
- [原有API文档](docs/modules_api.md) - 原有模块API说明

## 🎯 使用建议

1. **推荐使用数据分析**: `python generate_reports.py` - 生成完整分析报告
2. **快速分析**: `python run_analysis.py` - 生成基础分析报告
3. **数据统一管理**: 所有照片信息都保存在 `data/raw.csv` 中
4. **自动去重**: 处理过程中自动检测和去除重复照片
5. **多维度分析**: 利用分析功能了解拍摄习惯和设备使用情况

## 📄 许可证

本项目采用MIT许可证。
