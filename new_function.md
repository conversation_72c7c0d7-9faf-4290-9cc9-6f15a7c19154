# 生成分析py文件，根据以下要求统计照片数量，信息保存到report文件夹中的csv文件

## 设备使用频率: 统计不同拍摄设备拍摄的照片数量及占比。

## 创作时间段分析: 统计一天中不同时间段的照片数量。

## 季节性创作趋势: 统计不同季节的照片数量或月度趋势。

## 镜头使用频率: 统计各镜头型号拍摄的照片数量及占比。

## 根据光圈，焦距，快门速度和ISO区间统计照片数量：区间如下

- 焦距区间 (Focal Length Intervals - 35mm Equivalent)
< 24mm (超广角)
24mm - 34mm (广角)
35mm - 59mm (标准/中等广角)
60mm - 104mm (中焦/中远摄)
105mm - 199mm (短长焦)
200mm - 299mm (中长焦)
300mm - 399mm (长长焦)
400mm - 599mm (超长焦)
>= 600mm (极致超长焦)

- 光圈区间 (Aperture Intervals - f-stop)
f/1.6 或更大 (例如 f/1.0, f/1.2, f/1.4)
f/1.8 - f/2.8
f/3.2 - f/4.0
f/4.5 - f/6.3
f/7.1 - f/9.0
f/10 - f/14
f/16 - f/22
> f/22 (例如 f/25, f/29, f/32)

- 快门速度区间 (Shutter Speed Intervals - seconds)
1/2000s 及更快 (例如 1/4000s, 1/8000s)
1/500s - 1/1000s
1/125s - 1/250s
1/15s - 1/60s
1s - 1/8s
> 1s (长时间曝光，例如 2s, 10s, 30s, Bulb)

- ISO 感光度区间 (ISO Sensitivity Intervals)
ISO 100 - ISO 400
ISO 800 - ISO 1600
ISO 3200 - ISO 6400
> ISO 6400 (例如 ISO 12800, ISO 25600, Hi-ISO)
