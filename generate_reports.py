#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整报告生成脚本
生成所有分析报告，包括汇总报告和技术参数综合报告
"""

from modules.report_generator import ReportGenerator


def main():
    """主函数"""
    print("完整照片分析报告生成工具")
    print("=" * 50)
    
    # 创建报告生成器实例
    generator = ReportGenerator()
    
    # 生成所有报告
    success = generator.generate_all_reports()
    
    if success:
        print("\n完整报告生成完成！")
        print("请查看 data/reports/ 目录下的所有报告文件。")
        print("\n生成的报告包括：")
        print("- 基础分析报告（CSV格式）")
        print("- 汇总报告（TXT格式）")
        print("- 技术参数综合报告（CSV格式）")
    else:
        print("\n报告生成失败，请检查数据文件是否存在。")


if __name__ == "__main__":
    main()
